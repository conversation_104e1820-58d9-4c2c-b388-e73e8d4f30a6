import os
import pickle
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import Flow
from fastapi.responses import JSONResponse

CLIENT_SECRETS_FILE = "credentials.json"

SCOPES = [
    "https://www.googleapis.com/auth/drive",
    "https://www.googleapis.com/auth/drive.metadata.readonly",
    "https://www.googleapis.com/auth/spreadsheets"
]

REDIRECT_URI = "http://localhost:8000/auth/callback"


class SheetsApp:
    def __init__(self):
        self.token_file = "token.pickle"

    def get_flow(self):
        return Flow.from_client_secrets_file(
            CLIENT_SECRETS_FILE,
            scopes=SCOPES,
            redirect_uri=REDIRECT_URI,
        )

    def save_credentials(self, creds):
        with open(self.token_file, "wb") as token_file:
            pickle.dump(creds, token_file)

    def load_credentials(self):
        if not os.path.exists(self.token_file):
            return None
        with open(self.token_file, "rb") as token_file:
            return pickle.load(token_file)

    def list_sheets(self):
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("drive", "v3", credentials=creds)
        results = service.files().list(
            q="mimeType='application/vnd.google-apps.spreadsheet'",
            fields="files(id, name)"
        ).execute()

        return results.get("files", [])

    def get_sheet_details(self, spreadsheet_id: str):
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)
        sheet_metadata = (
            service.spreadsheets()
            .get(spreadsheetId=spreadsheet_id)
            .execute()
        )
        return {"sheets": sheet_metadata.get("sheets", []) }

    def get_sheet_values(self, spreadsheet_id: str, sheet_id: int):
        creds = self.load_credentials()
        if not creds:
            return JSONResponse({"error": "User not authenticated"}, status_code=401)

        service = build("sheets", "v4", credentials=creds)

        # Step 1: Fetch metadata to find sheet title by sheetId
        metadata = (
            service.spreadsheets()
            .get(spreadsheetId=spreadsheet_id)
            .execute()
        )

        sheet_title = None
        for sheet in metadata.get("sheets", []):
            if sheet["properties"]["sheetId"] == sheet_id:
                sheet_title = sheet["properties"]["title"]
                break

        if not sheet_title:
            return JSONResponse({"error": f"Sheet with id {sheet_id} not found"}, status_code=404)

        # Step 2: Fetch values from the sheet using the title
        result = (
            service.spreadsheets()
            .values()
            .get(spreadsheetId=spreadsheet_id, range=sheet_title)
            .execute()
        )

        return result.get("values", [])


