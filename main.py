from fastapi import <PERSON><PERSON><PERSON>, Request, Query, HTTPException
from fastapi.responses import RedirectResponse, JSONResponse
from pydantic import BaseModel
from sheets.sheets_app import SheetsApp
from agents.sheets_agent import SheetsReActAgent

app = FastAPI()
sheets_app = SheetsApp()

# Initialize the agent (will be created once and reused)
agent = None

class QueryRequest(BaseModel):
    query: str

def get_agent():
    """Get or create the agent instance"""
    global agent
    if agent is None:
        agent = SheetsReActAgent()
    return agent

@app.get("/auth/google")
def auth_google():
    flow = sheets_app.get_flow()
    auth_url, _ = flow.authorization_url(
        access_type="offline", include_granted_scopes="true"
    )
    return RedirectResponse(auth_url)

@app.get("/auth/callback")
def auth_callback(request: Request):
    code = request.query_params.get("code")
    if not code:
        return JSONResponse({"error": "No code provided"}, status_code=400)

    flow = sheets_app.get_flow()
    flow.fetch_token(code=code)
    creds = flow.credentials
    sheets_app.save_credentials(creds)

    return JSONResponse({"message": "Authentication successful!"})

@app.get("/sheets")
def list_google_sheets():
    return {"sheets": sheets_app.list_sheets()}

@app.get("/sheets/{spreadsheet_id}")
def get_sheet_details(spreadsheet_id: str):
    details = sheets_app.get_sheet_details(spreadsheet_id)
    return details

@app.get("/sheets/{spreadsheet_id}/values/{sheet_id}")
def get_sheet_values(spreadsheet_id: str, sheet_id: int):
    values = sheets_app.get_sheet_values(spreadsheet_id, sheet_id)
    return {"values": values}

@app.post("/agent/query")
def query_agent(request: QueryRequest):
    """
    Send a query to the Google Sheets ReAct agent.

    The agent can help you:
    - List all your Google Sheets
    - Get metadata about specific spreadsheets
    - Retrieve data from specific sheets
    - Answer questions about your sheets data

    Example queries:
    - "Show me all my Google Sheets"
    - "What sheets are in spreadsheet 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms?"
    - "Get data from sheet 0 in spreadsheet 1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
    """
    try:
        # Check if user is authenticated by trying to load credentials
        creds = sheets_app.load_credentials()
        if not creds:
            raise HTTPException(
                status_code=401,
                detail="User not authenticated. Please authenticate first using /auth/google"
            )

        # Get the agent and process the query
        sheets_agent = get_agent()
        response = sheets_agent.run(request.query)

        return {
            "query": request.query,
            "response": response,
            "status": "success"
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Handle any other errors
        raise HTTPException(
            status_code=500,
            detail=f"Error processing query: {str(e)}"
        )

@app.get("/agent/status")
def agent_status():
    """Check if the agent is ready and user is authenticated"""
    try:
        creds = sheets_app.load_credentials()
        if not creds:
            return {
                "authenticated": False,
                "agent_ready": False,
                "message": "User not authenticated. Please authenticate first using /auth/google"
            }

        # Try to initialize agent to check if it's working
        sheets_agent = get_agent()

        return {
            "authenticated": True,
            "agent_ready": True,
            "message": "Agent is ready to process queries"
        }

    except Exception as e:
        return {
            "authenticated": False,
            "agent_ready": False,
            "message": f"Error: {str(e)}"
        }
